# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [0.0.1] - 2025-01-14

### Added
- Initial release of XMTP v3 Flutter plugin
- Complete Android implementation with Kotlin
- XMTP v3 client creation and management
- Group chat functionality with full management features
- Direct messaging (DM) support
- Real-time message streaming
- Message history with pagination
- Optimistic message sending (prepare + publish)
- Message reactions support
- Comprehensive group management:
  - Add/remove members
  - Admin and Super Admin roles
  - Group permissions system
  - Group metadata (name, description, image)
  - Disappearing messages
- Consent management
- Comprehensive error handling with custom exception types
- Input validation for all API methods
- Complete data models for all XMTP v3 entities
- Comprehensive test suite
- Working Flutter example app
- Detailed documentation and README

### Technical Features
- Method channel communication between Flutter and Android
- Event channel support for real-time streaming
- Coroutine-based async operations in Kotlin
- Proper error propagation from native to Dart
- Memory-efficient data serialization
- Support for XMTP Android library integration

### Platform Support
- ✅ Android (API 21+)
- ⏳ iOS (planned for future release)

### Dependencies
- Flutter SDK 3.0+
- Android: XMTP Android library (latest)
- Kotlin support
- Java 17 compatibility

### Known Limitations
- iOS implementation not yet available
- Some advanced XMTP features may require future updates
- Real-time streaming requires active network connection

### Breaking Changes
- None (initial release)

### Migration Guide
- None (initial release)
