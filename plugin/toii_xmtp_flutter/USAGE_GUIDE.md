# TOII XMTP Flutter Plugin - Usage Guide

## Overview

The TOII XMTP Flutter plugin provides a Flutter interface for the XMTP (Extensible Message Transport Protocol) v3 messaging system. This plugin allows you to build decentralized messaging applications with support for:

- 🔐 Wallet-based authentication
- 💬 Direct messages (DMs)
- 👥 Group conversations
- 📱 Real-time message streaming
- 🎛️ Advanced conversation management
- 🔄 Cross-device synchronization

## Tính năng được hỗ trợ / Supported Features

- ✅ Tạo và quản lý XMTP client
- ✅ Tạo và quản lý group conversations  
- ✅ Tạo và quản lý direct messages
- ✅ Gửi và nhận tin nhắn real-time
- ✅ Quản lý thành viên nhóm và quyền admin
- ✅ Streaming messages và conversations
- ✅ Đồng bộ hóa dữ liệu
- ✅ Cấu hình environment (dev/production)

## Installation

Add this to your package's `pubspec.yaml` file:

```yaml
dependencies:
  toii_xmtp_flutter: ^1.0.0
```

Then run:

```bash
flutter pub get
```

## Quick Start

### 1. Initialize the Plugin

```dart
import 'package:toii_xmtp_flutter/toii_xmtp_flutter.dart';

class MyApp extends StatefulWidget {
  @override
  _MyAppState createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  final _xmtpPlugin = ToiiXmtpFlutter();
  XmtpClient? _client;

  @override
  Widget build(BuildContext context) {
    // Your app UI
  }
}
```

### 2. Create an XMTP Client

```dart
Future<void> createClient() async {
  try {
    // Configure client options
    final options = ClientOptions(
      environment: 'dev', // or 'production'
      deviceSyncEnabled: true,
      debugEventsEnabled: true,
    );

    // Create client with private key
    final client = await _xmtpPlugin.createClient(
      privateKey: 'your_private_key_here',
      options: options,
    );

    setState(() {
      _client = client;
    });

    print('Client created with inbox ID: ${client.inboxId}');
  } catch (e) {
    print('Error creating client: $e');
  }
}
```

### 3. Create a Direct Message

```dart
Future<void> createDirectMessage() async {
  try {
    final dm = await _xmtpPlugin.findOrCreateDm('target_inbox_id');
    print('DM created with: ${dm.peerInboxId}');
  } catch (e) {
    print('Error creating DM: $e');
  }
}
```

### 4. Send a Message

```dart
Future<void> sendMessage(String conversationId, String content) async {
  try {
    final messageId = await _xmtpPlugin.sendMessage(
      conversationId: conversationId,
      content: content,
    );
    print('Message sent with ID: $messageId');
  } catch (e) {
    print('Error sending message: $e');
  }
}
```

## API Reference

### Client Management

#### `getPlatformVersion()`
Gets the platform version information.

```dart
Future<String?> platformVersion = _xmtpPlugin.getPlatformVersion();
```

#### `createClient({required String privateKey, ClientOptions? options})`
Creates a new XMTP client with the specified private key.

```dart
final client = await _xmtpPlugin.createClient(
  privateKey: '0x1234...', 
  options: ClientOptions(
    environment: 'dev',
    deviceSyncEnabled: true,
    debugEventsEnabled: false,
  ),
);
```

#### `getInboxId()`
Gets the inbox ID of the current client.

```dart
String inboxId = await _xmtpPlugin.getInboxId();
```

#### `getInboxIdFromIdentity(String identity, {String environment = 'dev'})`
Gets inbox ID from an identity/address.

```dart
String inboxId = await _xmtpPlugin.getInboxIdFromIdentity(
  '0xAddress...',
  environment: 'dev',
);
```

### Conversation Management

#### `listConversations({bool includeGroups = true, bool includeDms = true})`
Lists all conversations (groups and/or DMs).

```dart
List<Conversation> conversations = await _xmtpPlugin.listConversations(
  includeGroups: true,
  includeDms: true,
);
```

#### `syncConversations()`
Synchronizes conversations with the network.

```dart
await _xmtpPlugin.syncConversations();
```

#### `streamConversations({bool includeGroups = true, bool includeDms = true})`
Creates a stream for real-time conversation updates.

```dart
Stream<Conversation> conversationStream = _xmtpPlugin.streamConversations();

conversationStream.listen((conversation) {
  print('New conversation: ${conversation.id}');
});
```

### Group Management

#### `createGroup({required List<String> memberInboxIds, String? groupName, String? groupDescription, String? groupImageUrl})`
Creates a new group conversation.

```dart
final group = await _xmtpPlugin.createGroup(
  memberInboxIds: ['inbox1', 'inbox2', 'inbox3'],
  groupName: 'My Group',
  groupDescription: 'A group for discussing Flutter',
  groupImageUrl: 'https://example.com/image.jpg',
);
```

#### `listGroups()`
Lists all group conversations.

```dart
List<Group> groups = await _xmtpPlugin.listGroups();
```

#### `syncGroup(String groupId)`
Synchronizes a specific group with the network.

```dart
await _xmtpPlugin.syncGroup('group_id');
```

#### Group Member Management

```dart
// Add members to group
await _xmtpPlugin.addMembers('group_id', ['new_inbox_1', 'new_inbox_2']);

// Remove members from group
await _xmtpPlugin.removeMembers('group_id', ['inbox_to_remove']);

// Add admin
await _xmtpPlugin.addAdmin('group_id', 'inbox_id');

// Remove admin
await _xmtpPlugin.removeAdmin('group_id', 'inbox_id');

// Add super admin
await _xmtpPlugin.addSuperAdmin('group_id', 'inbox_id');

// Remove super admin
await _xmtpPlugin.removeSuperAdmin('group_id', 'inbox_id');
```

#### Group Settings Management

```dart
// Update group name
await _xmtpPlugin.updateGroupName('group_id', 'New Group Name');

// Update group description
await _xmtpPlugin.updateGroupDescription('group_id', 'New description');

// Update group image
await _xmtpPlugin.updateGroupImageUrl('group_id', 'https://new-image.com');

// Update consent state
await _xmtpPlugin.updateConsentState('group_id', ConsentState.allowed);

// Set disappearing messages
await _xmtpPlugin.updateDisappearingMessageSettings(
  'group_id',
  DisappearingMessageSettings(
    enabled: true,
    duration: Duration(hours: 24),
  ),
);

// Clear disappearing messages
await _xmtpPlugin.clearDisappearingMessageSettings('group_id');
```

### Direct Message Management

#### `findOrCreateDm(String targetInboxId)`
Finds existing DM or creates a new one with the target inbox.

```dart
final dm = await _xmtpPlugin.findOrCreateDm('target_inbox_id');
```

#### `listDms()`
Lists all direct message conversations.

```dart
List<Dm> dms = await _xmtpPlugin.listDms();
```

### Message Management

#### `sendMessage({required String conversationId, required String content, SendOptions? options})`
Sends a message to a conversation.

```dart
final messageId = await _xmtpPlugin.sendMessage(
  conversationId: 'conversation_id',
  content: 'Hello, world!',
  options: SendOptions(
    // Add any send options here
  ),
);
```

#### `getMessages(String conversationId, {MessageQueryOptions? options})`
Gets messages from a conversation.

```dart
List<XmtpMessage> messages = await _xmtpPlugin.getMessages(
  'conversation_id',
  options: MessageQueryOptions(
    limit: 50,
    // Add other query options
  ),
);
```

#### `getMessagesWithReactions(String conversationId, {int limit = 50})`
Gets messages with their reactions.

```dart
List<MessageWithReactions> messages = await _xmtpPlugin.getMessagesWithReactions(
  'conversation_id',
  limit: 50,
);
```

#### `streamMessages(String conversationId)`
Creates a stream for real-time message updates.

```dart
Stream<XmtpMessage> messageStream = _xmtpPlugin.streamMessages('conversation_id');

messageStream.listen((message) {
  print('New message: ${message.content}');
});
```

#### `prepareMessage(String conversationId, String content)`
Prepares a message for sending (useful for offline scenarios).

```dart
String preparedMessageId = await _xmtpPlugin.prepareMessage(
  'conversation_id',
  'Message content',
);
```

#### `publishMessages(String conversationId)`
Publishes prepared messages to the network.

```dart
await _xmtpPlugin.publishMessages('conversation_id');
```

## Complete Example

Here's a complete example showing how to build a simple messaging app:

```dart
import 'package:flutter/material.dart';
import 'package:toii_xmtp_flutter/toii_xmtp_flutter.dart';

class MessagingApp extends StatefulWidget {
  @override
  _MessagingAppState createState() => _MessagingAppState();
}

class _MessagingAppState extends State<MessagingApp> {
  final _xmtpPlugin = ToiiXmtpFlutter();
  XmtpClient? _client;
  List<Conversation> _conversations = [];
  String _status = 'Ready';

  @override
  void initState() {
    super.initState();
    _initializeClient();
  }

  Future<void> _initializeClient() async {
    try {
      setState(() => _status = 'Creating client...');
      
      final options = ClientOptions(
        environment: 'dev',
        deviceSyncEnabled: true,
        debugEventsEnabled: true,
      );

      final client = await _xmtpPlugin.createClient(
        privateKey: 'your_private_key_here',
        options: options,
      );

      setState(() {
        _client = client;
        _status = 'Client created: ${client.inboxId}';
      });

      await _loadConversations();
    } catch (e) {
      setState(() => _status = 'Error: $e');
    }
  }

  Future<void> _loadConversations() async {
    try {
      setState(() => _status = 'Loading conversations...');
      
      final conversations = await _xmtpPlugin.listConversations();
      final dms = await _xmtpPlugin.listDms();

      setState(() {
        _conversations = [...conversations, ...dms];
        _status = 'Loaded ${_conversations.length} conversations';
      });
    } catch (e) {
      setState(() => _status = 'Error loading conversations: $e');
    }
  }

  Future<void> _createDm(String targetInboxId) async {
    try {
      final dm = await _xmtpPlugin.findOrCreateDm(targetInboxId);
      await _loadConversations();
    } catch (e) {
      setState(() => _status = 'Error creating DM: $e');
    }
  }

  Future<void> _sendMessage(String conversationId, String content) async {
    try {
      await _xmtpPlugin.sendMessage(
        conversationId: conversationId,
        content: content,
      );
    } catch (e) {
      setState(() => _status = 'Error sending message: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('XMTP Messaging'),
      ),
      body: Column(
        children: [
          // Status bar
          Container(
            padding: EdgeInsets.all(16),
            child: Text(_status),
          ),
          
          // Conversations list
          Expanded(
            child: ListView.builder(
              itemCount: _conversations.length,
              itemBuilder: (context, index) {
                final conversation = _conversations[index];
                return ListTile(
                  title: Text(conversation.id),
                  subtitle: Text('Type: ${conversation.type}'),
                  onTap: () {
                    // Navigate to chat screen
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => ChatScreen(
                          conversation: conversation,
                          xmtpPlugin: _xmtpPlugin,
                        ),
                      ),
                    );
                  },
                );
              },
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          // Show create conversation dialog
          _showCreateConversationDialog();
        },
        child: Icon(Icons.add),
      ),
    );
  }

  void _showCreateConversationDialog() {
    showDialog(
      context: context,
      builder: (context) {
        String targetInboxId = '';
        return AlertDialog(
          title: Text('Create DM'),
          content: TextField(
            onChanged: (value) => targetInboxId = value,
            decoration: InputDecoration(
              labelText: 'Target Inbox ID',
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                _createDm(targetInboxId);
              },
              child: Text('Create'),
            ),
          ],
        );
      },
    );
  }
}
```

## Error Handling

Always wrap XMTP operations in try-catch blocks:

```dart
try {
  final result = await _xmtpPlugin.someOperation();
  // Handle success
} catch (e) {
  if (e.toString().contains('network')) {
    // Handle network errors
  } else if (e.toString().contains('permission')) {
    // Handle permission errors
  } else {
    // Handle other errors
  }
}
```

## Best Practices

1. **Always check if client is created** before performing operations
2. **Use streams** for real-time updates in your UI
3. **Implement proper error handling** for network operations
4. **Sync conversations** periodically to ensure consistency
5. **Use prepared messages** for better offline support
6. **Store wallet credentials securely** (use flutter_secure_storage)

## Environment Configuration

### Development Environment
```dart
ClientOptions(
  environment: 'dev',
  deviceSyncEnabled: true,
  debugEventsEnabled: true,
)
```

### Production Environment
```dart
ClientOptions(
  environment: 'production',
  deviceSyncEnabled: true,
  debugEventsEnabled: false,
)
```

## Troubleshooting

### Common Issues

1. **Client creation fails**: Check private key format and network connection
2. **Messages not syncing**: Call `syncConversations()` or `syncGroup()`
3. **Permission errors**: Ensure proper wallet access
4. **Network timeouts**: Implement retry logic with exponential backoff

### Debug Tips

- Enable debug events in development: `debugEventsEnabled: true`
- Use platform version to check plugin status
- Monitor console logs for detailed error messages
- Test with dev environment before production deployment

## Support

For issues and questions:
- Check the example app for reference implementations
- Review error messages carefully for specific guidance
- Ensure you're using the latest plugin version

## Contributing

Contributions are welcome! Please follow the standard Flutter plugin development guidelines.