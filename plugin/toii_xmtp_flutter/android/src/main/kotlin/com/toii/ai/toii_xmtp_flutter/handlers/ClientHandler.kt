package com.toii.ai.toii_xmtp_flutter.handlers

import android.content.Context
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel.Result
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import org.xmtp.android.library.Client
import org.xmtp.android.library.ClientOptions
import org.xmtp.android.library.XMTPEnvironment
import org.xmtp.android.library.SigningKey
import org.xmtp.android.library.SignerType
import org.xmtp.android.library.SignedData
import org.xmtp.android.library.libxmtp.PublicIdentity
import org.xmtp.android.library.libxmtp.IdentityKind
import java.io.File
import java.security.SecureRandom
import java.security.MessageDigest
import org.web3j.crypto.ECKeyPair
import org.web3j.crypto.Keys
import org.web3j.crypto.Sign
import org.web3j.utils.Numeric
import java.math.BigInteger
import org.xmtp.android.library.Util

// Simple EOA wallet implementation for XMTP
class SimpleEOAWallet(private val privateKey: String) : SigningKey {
    private val keyPair: ECKeyPair by lazy {
        // Parse the private key and create ECKeyPair
        val privateKeyBigInt = if (privateKey.startsWith("0x")) {
            BigInteger(privateKey.substring(2), 16)
        } else {
            BigInteger(privateKey, 16)
        }
        ECKeyPair.create(privateKeyBigInt)
    }

    private val derivedAddress: String by lazy {
        // Derive the Ethereum address from the public key
        "0x" + Keys.getAddress(keyPair)
    }

    override val publicIdentity: PublicIdentity
        get() = PublicIdentity(IdentityKind.ETHEREUM, derivedAddress)

    override val type: SignerType
        get() = SignerType.EOA

    override suspend fun sign(message: String): SignedData {
        try {
            // Use the same message hashing as XMTP library (with Ethereum message prefix)
            val digest = ethHash(message)

            // Sign the hash using the private key
            val signatureData = Sign.signMessage(digest, keyPair, false)

            // Format the signature bytes using the same logic as XMTP KeyUtil
            val fullSignature = getSignatureBytes(signatureData)

            return SignedData(
                rawData = fullSignature,
                publicKey = null, // Not needed for EOA
                authenticatorData = null,
                clientDataJson = null
            )
        } catch (e: Exception) {
            throw RuntimeException("Failed to sign message: ${e.message}", e)
        }
    }

    // Implement the same ethHash logic as XMTP KeyUtil
    private fun ethHash(message: String): ByteArray {
        val messagePrefix = "\u0019Ethereum Signed Message:\n"
        val input = messagePrefix + message.length + message
        return Util.keccak256(input.toByteArray())
    }

    // Implement the same signature formatting as XMTP KeyUtil
    private fun getSignatureBytes(sig: Sign.SignatureData): ByteArray {
        val v = sig.v[0]
        val fixedV = if (v >= 27) (v - 27).toByte() else v
        return merge(
            sig.r,
            sig.s,
            byteArrayOf(fixedV),
        )
    }

    // Helper method to merge byte arrays
    private fun merge(vararg arrays: ByteArray): ByteArray {
        var count = 0
        for (array in arrays) {
            count += array.size
        }

        val mergedArray = ByteArray(count)
        var start = 0
        for (array in arrays) {
            System.arraycopy(array, 0, mergedArray, start, array.size)
            start += array.size
        }
        return mergedArray
    }

    // Helper property to get the address
    val address: String
        get() = derivedAddress
}

class ClientHandler(private val context: Context) {
    companion object {
        private var client: Client? = null

        fun getClient(): Client? = client
    }

    fun createClient(call: MethodCall, result: Result, scope: CoroutineScope) {
        scope.launch {
            try {
                val privateKey = call.argument<String>("privateKey")
                    ?: throw IllegalArgumentException("privateKey is required")

                val environment = call.argument<String>("environment") ?: "dev"
                val appVersion = call.argument<String>("appVersion")
                val dbDirectory = call.argument<String>("dbDirectory")
                val historySyncUrl = call.argument<String>("historySyncUrl")
                val deviceSyncEnabled = call.argument<Boolean>("deviceSyncEnabled") ?: true
                val debugEventsEnabled = call.argument<Boolean>("debugEventsEnabled") ?: false

                // Create the EOA wallet with the private key
                val wallet = SimpleEOAWallet(privateKey)

                // Create client options
                val xmtpEnv = when (environment.lowercase()) {
                    "production" -> XMTPEnvironment.PRODUCTION
                    "local" -> XMTPEnvironment.LOCAL
                    else -> XMTPEnvironment.DEV
                }

                // Generate a proper encryption key for the database
                val dbEncryptionKey = generateDbEncryptionKey(privateKey)

                val clientOptions = ClientOptions(
                    api = ClientOptions.Api(xmtpEnv, true),
                    appContext = context,
                    dbEncryptionKey = dbEncryptionKey
                )

                // Create the XMTP client using the correct API
                client = Client.create(
                    account = wallet,
                    options = clientOptions
                )

                // Debug logging
                android.util.Log.d("XMTP", "Client created successfully")
                android.util.Log.d("XMTP", "InboxId: ${client!!.inboxId}")
                android.util.Log.d("XMTP", "InstallationId: ${client!!.installationId}")
                android.util.Log.d("XMTP", "Environment: $environment")
                android.util.Log.d("XMTP", "Address: ${wallet.address}")
                android.util.Log.d("XMTP", "DbPath: ${client!!.dbPath}")

                val clientData = mapOf(
                    "inboxId" to client!!.inboxId,
                    "installationId" to client!!.installationId,
                    "environment" to environment,
                    "dbPath" to client!!.dbPath,
                    "publicIdentity" to mapOf(
                        "kind" to "ethereum",
                        "identifier" to wallet.address
                    )
                )

                result.success(clientData)
            } catch (e: Exception) {
                result.error("CLIENT_CREATION_ERROR", "Failed to create client: ${e.message}", e.toString())
            }
        }
    }
    
    fun getInboxId(call: MethodCall, result: Result) {
        try {
            val currentClient = client
            if (currentClient == null) {
                result.error("NO_CLIENT", "Client not initialized", null)
                return
            }
            
            result.success(currentClient.inboxId)
        } catch (e: Exception) {
            result.error("GET_INBOX_ID_ERROR", "Failed to get inbox ID: ${e.message}", e.toString())
        }
    }
    
    fun getInboxIdFromIdentity(call: MethodCall, result: Result, scope: CoroutineScope) {
        scope.launch {
            try {
                val identity = call.argument<String>("identity")
                    ?: throw IllegalArgumentException("identity is required")

                val environment = call.argument<String>("environment") ?: "dev"

                // Create a PublicIdentity from the provided identity string
                val publicIdentity = PublicIdentity(IdentityKind.ETHEREUM, identity)

                // Create client options for the API call
                val xmtpEnv = when (environment.lowercase()) {
                    "production" -> XMTPEnvironment.PRODUCTION
                    "local" -> XMTPEnvironment.LOCAL
                    else -> XMTPEnvironment.DEV
                }

                val api = ClientOptions.Api(xmtpEnv, true)

                // Get inbox ID from identity using the static method
                val inboxId = Client.getOrCreateInboxId(api, publicIdentity)

                result.success(inboxId)
            } catch (e: Exception) {
                result.error("GET_INBOX_ID_FROM_IDENTITY_ERROR", "Failed to get inbox ID from identity: ${e.message}", e.toString())
            }
        }
    }
    
    private fun generateDbEncryptionKey(privateKey: String): ByteArray {
        // Generate a deterministic 32-byte encryption key using SHA-256
        // This ensures the same key is used for the same wallet across sessions
        val digest = MessageDigest.getInstance("SHA-256")
        val keyMaterial = "xmtp_db_key_$privateKey".toByteArray(Charsets.UTF_8)
        return digest.digest(keyMaterial)
    }
    

}
