package com.toii.ai.toii_xmtp_flutter.handlers

import android.util.Log
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel.Result
import io.flutter.plugin.common.EventChannel
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.flow.collect
import org.xmtp.android.library.Client
import org.xmtp.android.library.Dm
import org.xmtp.android.library.Group
import org.xmtp.android.library.libxmtp.DecodedMessage
import org.xmtp.android.library.codecs.id

class MessageHandler {
    
    fun sendMessage(call: MethodCall, result: Result, scope: CoroutineScope) {
        scope.launch {
            try {
                val client = getClientFromCall(call, result) ?: return@launch
                val conversationId = call.argument<String>("conversationId")
                    ?: return@launch result.error("INVALID_ARGUMENT", "conversationId is required", null)
                val content = call.argument<String>("content")
                    ?: return@launch result.error("INVALID_ARGUMENT", "content is required", null)

                Log.d("XMTP", "Sending message to conversation: $conversationId")

                // Find the conversation with improved lookup and sync
                val conversation = findConversationWithSync(client, conversationId)

                if (conversation == null) {
                    Log.e("XMTP", "Conversation not found after sync attempts: $conversationId")
                    return@launch result.error("CONVERSATION_NOT_FOUND", "Conversation not found: $conversationId", null)
                }

                // Sync conversation first to ensure we have latest state
                Log.d("XMTP", "Syncing conversation before sending message: $conversationId")
                when (conversation) {
                    is org.xmtp.android.library.Dm -> conversation.sync()
                    is org.xmtp.android.library.Group -> conversation.sync()
                }

                // Send message based on conversation type
                val messageId = when (conversation) {
                    is org.xmtp.android.library.Dm -> {
                        Log.d("XMTP", "Sending DM message: $content")
                        conversation.send(content)
                    }
                    is org.xmtp.android.library.Group -> {
                        Log.d("XMTP", "Sending Group message: $content")
                        conversation.send(content)
                    }
                    else -> {
                        return@launch result.error("UNSUPPORTED_CONVERSATION_TYPE", "Unsupported conversation type", null)
                    }
                }

                Log.d("XMTP", "Message sent successfully with ID: $messageId")
                result.success(messageId)
            } catch (e: Exception) {
                Log.e("XMTP", "Failed to send message: ${e.message}", e)
                result.error("SEND_MESSAGE_ERROR", "Failed to send message: ${e.message}", e.toString())
            }
        }
    }
    
    fun getMessages(call: MethodCall, result: Result, scope: CoroutineScope) {
        scope.launch {
            try {
                val client = getClientFromCall(call, result) ?: return@launch
                val conversationId = call.argument<String>("conversationId")
                    ?: return@launch result.error("INVALID_ARGUMENT", "conversationId is required", null)

                Log.d("XMTP", "Getting messages for conversation: $conversationId")

                // Find the conversation with improved lookup and sync
                val conversation = findConversationWithSync(client, conversationId)

                if (conversation == null) {
                    Log.e("XMTP", "Conversation not found after sync attempts: $conversationId")
                    return@launch result.error("CONVERSATION_NOT_FOUND", "Conversation not found: $conversationId", null)
                }

                // Sync conversation first to ensure we have latest messages
                Log.d("XMTP", "Syncing conversation before getting messages: $conversationId")
                when (conversation) {
                    is org.xmtp.android.library.Dm -> conversation.sync()
                    is org.xmtp.android.library.Group -> conversation.sync()
                }

                // Get query options
                val limit = call.argument<Int>("limit") ?: 50
                val beforeNs = call.argument<Long>("beforeNs")
                val afterNs = call.argument<Long>("afterNs")

                Log.d("XMTP", "Retrieving messages with limit: $limit")

                // Get messages based on conversation type
                val messages = when (conversation) {
                    is org.xmtp.android.library.Dm -> {
                        conversation.messages(
                            limit = limit,
                            beforeNs = beforeNs,
                            afterNs = afterNs
                        )
                    }
                    is org.xmtp.android.library.Group -> {
                        conversation.messages(
                            limit = limit,
                            beforeNs = beforeNs,
                            afterNs = afterNs
                        )
                    }
                    else -> {
                        return@launch result.error("UNSUPPORTED_CONVERSATION_TYPE", "Unsupported conversation type", null)
                    }
                }

                // Convert messages to map format, filtering out invalid messages
                val messageList = messages.mapNotNull { message ->
                    try {
                        // Skip messages with null/empty ID
                        if (message.id.isNullOrEmpty()) {
                            Log.w("XMTP", "Skipping message with null/empty ID")
                            return@mapNotNull null
                        }

                        mapOf(
                            "id" to message.id,
                            "conversationId" to conversationId,
                            "senderInboxId" to (message.senderInboxId ?: ""),
                            "content" to (message.body ?: ""),
                            "contentType" to (message.encodedContent.type.id ?: "text/plain"),
                            "sentAt" to message.sentAt.time,
                            "deliveryStatus" to when (message.deliveryStatus) {
                                org.xmtp.android.library.libxmtp.DecodedMessage.MessageDeliveryStatus.PUBLISHED -> "published"
                                org.xmtp.android.library.libxmtp.DecodedMessage.MessageDeliveryStatus.UNPUBLISHED -> "unpublished"
                                org.xmtp.android.library.libxmtp.DecodedMessage.MessageDeliveryStatus.FAILED -> "failed"
                                else -> "published"
                            }
                        )
                    } catch (e: Exception) {
                        Log.e("XMTP", "Error converting message: ${e.message}", e)
                        null
                    }
                }

                Log.d("XMTP", "Retrieved ${messageList.size} messages")
                result.success(messageList)
            } catch (e: Exception) {
                Log.e("XMTP", "Error getting messages: ${e.message}", e)
                result.error("GET_MESSAGES_ERROR", "Failed to get messages: ${e.message}", e.toString())
            }
        }
    }

    fun listMessages(call: MethodCall, result: Result, scope: CoroutineScope) {
        scope.launch {
            try {
                val client = getClientFromCall(call, result) ?: return@launch

                // TODO: Implement message listing
                result.success(emptyList<Map<String, Any>>())
            } catch (e: Exception) {
                result.error("LIST_MESSAGES_ERROR", "Failed to list messages: ${e.message}", e.toString())
            }
        }
    }
    
    // Note: streamMessages method has been moved to centralized StreamManager
    // to eliminate EventChannel conflicts. The plugin-level handler now manages all streaming.

    private fun getClientFromCall(call: MethodCall, result: Result): Client? {
        return ClientHandler.getClient() ?: run {
            result.error("NO_CLIENT", "Client not initialized", null)
            null
        }
    }

    /**
     * Find conversation with improved lookup that includes syncing
     */
    private suspend fun findConversationWithSync(client: Client, conversationId: String): Any? {
        Log.d("XMTP", "Finding conversation with sync: $conversationId")

        // First attempt: Try to find in current lists
        var conversation = findConversationInLists(client, conversationId)
        if (conversation != null) {
            Log.d("XMTP", "Found conversation in current lists: $conversationId")
            return conversation
        }

        // Second attempt: Sync conversations and try again
        try {
            Log.d("XMTP", "Syncing conversations before retry: $conversationId")
            client.conversations.sync()
            conversation = findConversationInLists(client, conversationId)
            if (conversation != null) {
                Log.d("XMTP", "Found conversation after sync: $conversationId")
                return conversation
            }
        } catch (e: Exception) {
            Log.e("XMTP", "Error syncing conversations: ${e.message}")
        }

        // Third attempt: Try using the native findConversation method if available
        try {
            Log.d("XMTP", "Trying native findConversation method: $conversationId")
            val nativeConversation = client.conversations.findConversation(conversationId)
            if (nativeConversation != null) {
                Log.d("XMTP", "Found conversation using native method: $conversationId")
                return when (nativeConversation) {
                    is org.xmtp.android.library.Conversation.Dm -> nativeConversation.dm
                    is org.xmtp.android.library.Conversation.Group -> nativeConversation.group
                }
            }
        } catch (e: Exception) {
            Log.e("XMTP", "Error using native findConversation: ${e.message}")
        }

        Log.w("XMTP", "Conversation not found after all attempts: $conversationId")
        return null
    }

    /**
     * Find conversation in current DM and Group lists
     */
    private suspend fun findConversationInLists(client: Client, conversationId: String): Any? {
        return try {
            // Try to find as DM first
            client.conversations.listDms().find { it.id == conversationId }
                ?: client.conversations.listGroups().find { it.id == conversationId }
        } catch (e: Exception) {
            Log.e("XMTP", "Error searching conversation lists: ${e.message}")
            null
        }
    }
}


