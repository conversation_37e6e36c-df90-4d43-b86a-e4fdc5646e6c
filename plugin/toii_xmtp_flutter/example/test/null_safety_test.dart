import 'package:flutter_test/flutter_test.dart';
import 'package:toii_xmtp_flutter/toii_xmtp_flutter.dart';

void main() {
  group('Null Safety Tests', () {
    test('XmtpMessage.fromMap should handle null values gracefully', () {
      // Test with null id
      expect(
        () => XmtpMessage.fromMap({
          'id': null,
          'conversationId': 'conv123',
          'senderInboxId': 'sender123',
          'content': 'Hello',
          'contentType': 'text/plain',
          'sentAt': 1234567890,
          'deliveryStatus': 'published',
        }),
        throwsA(isA<ArgumentError>()),
      );

      // Test with null conversationId
      expect(
        () => XmtpMessage.fromMap({
          'id': 'msg123',
          'conversationId': null,
          'senderInboxId': 'sender123',
          'content': 'Hello',
          'contentType': 'text/plain',
          'sentAt': 1234567890,
          'deliveryStatus': 'published',
        }),
        throwsA(isA<ArgumentError>()),
      );

      // Test with null senderInboxId
      expect(
        () => XmtpMessage.fromMap({
          'id': 'msg123',
          'conversationId': 'conv123',
          'senderInboxId': null,
          'content': 'Hello',
          'contentType': 'text/plain',
          'sentAt': 1234567890,
          'deliveryStatus': 'published',
        }),
        throwsA(isA<ArgumentError>()),
      );

      // Test with null content
      expect(
        () => XmtpMessage.fromMap({
          'id': 'msg123',
          'conversationId': 'conv123',
          'senderInboxId': 'sender123',
          'content': null,
          'contentType': 'text/plain',
          'sentAt': 1234567890,
          'deliveryStatus': 'published',
        }),
        throwsA(isA<ArgumentError>()),
      );

      // Test with valid data should work
      expect(
        () => XmtpMessage.fromMap({
          'id': 'msg123',
          'conversationId': 'conv123',
          'senderInboxId': 'sender123',
          'content': 'Hello',
          'contentType': 'text/plain',
          'sentAt': 1234567890,
          'deliveryStatus': 'published',
        }),
        returnsNormally,
      );
    });
  });
}
