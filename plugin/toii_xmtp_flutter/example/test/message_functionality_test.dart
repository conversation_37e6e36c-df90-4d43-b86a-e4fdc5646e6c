import 'package:flutter_test/flutter_test.dart';
import 'package:toii_xmtp_flutter/toii_xmtp_flutter.dart';

void main() {
  group('Message Functionality Tests', () {
    test('XmtpMessage should handle all delivery status values', () {
      // Test published status
      final publishedMessage = XmtpMessage.fromMap({
        'id': 'test-id-1',
        'conversationId': 'test-conversation-1',
        'senderInboxId': 'test-sender-1',
        'content': 'Test message content',
        'contentType': 'xmtp.org:text:1.0',
        'sentAt': DateTime.now().millisecondsSinceEpoch,
        'deliveryStatus': 'published',
      });
      
      expect(publishedMessage.deliveryStatus, MessageDeliveryStatus.published);
      expect(publishedMessage.content, 'Test message content');
      
      // Test unpublished status
      final unpublishedMessage = XmtpMessage.fromMap({
        'id': 'test-id-2',
        'conversationId': 'test-conversation-2',
        'senderInboxId': 'test-sender-2',
        'content': 'Test unpublished message',
        'contentType': 'xmtp.org:text:1.0',
        'sentAt': DateTime.now().millisecondsSinceEpoch,
        'deliveryStatus': 'unpublished',
      });
      
      expect(unpublishedMessage.deliveryStatus, MessageDeliveryStatus.unpublished);
      
      // Test failed status
      final failedMessage = XmtpMessage.fromMap({
        'id': 'test-id-3',
        'conversationId': 'test-conversation-3',
        'senderInboxId': 'test-sender-3',
        'content': 'Test failed message',
        'contentType': 'xmtp.org:text:1.0',
        'sentAt': DateTime.now().millisecondsSinceEpoch,
        'deliveryStatus': 'failed',
      });
      
      expect(failedMessage.deliveryStatus, MessageDeliveryStatus.failed);
    });

    test('XmtpMessage should throw error for invalid delivery status', () {
      expect(
        () => XmtpMessage.fromMap({
          'id': 'test-id',
          'conversationId': 'test-conversation',
          'senderInboxId': 'test-sender',
          'content': 'Test message',
          'contentType': 'xmtp.org:text:1.0',
          'sentAt': DateTime.now().millisecondsSinceEpoch,
          'deliveryStatus': 'invalid-status',
        }),
        throwsA(isA<ArgumentError>()),
      );
    });

    test('MessageDeliveryStatus enum should have correct values', () {
      expect(MessageDeliveryStatus.published.value, 'published');
      expect(MessageDeliveryStatus.unpublished.value, 'unpublished');
      expect(MessageDeliveryStatus.failed.value, 'failed');
      expect(MessageDeliveryStatus.all.value, 'all');
    });

    test('MessageDeliveryStatus.fromString should work correctly', () {
      expect(MessageDeliveryStatus.fromString('published'), MessageDeliveryStatus.published);
      expect(MessageDeliveryStatus.fromString('unpublished'), MessageDeliveryStatus.unpublished);
      expect(MessageDeliveryStatus.fromString('failed'), MessageDeliveryStatus.failed);
      expect(MessageDeliveryStatus.fromString('all'), MessageDeliveryStatus.all);
      
      // Test case insensitive
      expect(MessageDeliveryStatus.fromString('PUBLISHED'), MessageDeliveryStatus.published);
      expect(MessageDeliveryStatus.fromString('UnPublished'), MessageDeliveryStatus.unpublished);
    });

    test('XmtpMessage should handle metadata correctly', () {
      final messageWithMetadata = XmtpMessage.fromMap({
        'id': 'test-id',
        'conversationId': 'test-conversation',
        'senderInboxId': 'test-sender',
        'content': 'Test message',
        'contentType': 'xmtp.org:text:1.0',
        'sentAt': DateTime.now().millisecondsSinceEpoch,
        'deliveryStatus': 'published',
        'metadata': {'key1': 'value1', 'key2': 'value2'},
      });
      
      expect(messageWithMetadata.metadata, isNotNull);
      expect(messageWithMetadata.metadata!['key1'], 'value1');
      expect(messageWithMetadata.metadata!['key2'], 'value2');
      
      final messageWithoutMetadata = XmtpMessage.fromMap({
        'id': 'test-id',
        'conversationId': 'test-conversation',
        'senderInboxId': 'test-sender',
        'content': 'Test message',
        'contentType': 'xmtp.org:text:1.0',
        'sentAt': DateTime.now().millisecondsSinceEpoch,
        'deliveryStatus': 'published',
      });
      
      expect(messageWithoutMetadata.metadata, isNull);
    });
  });
}
