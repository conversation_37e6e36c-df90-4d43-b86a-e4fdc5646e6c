// XMTP Flutter Example App Tests
//
// Comprehensive test suite for the XMTP Flutter plugin example app.
// Tests UI components, user interactions, and XMTP functionality.

import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:toii_xmtp_flutter/toii_xmtp_flutter.dart';
import 'package:toii_xmtp_flutter_example/main.dart';

void main() {
  group('XMTP Flutter Example App Tests', () {
    testWidgets('App should load with initial UI elements', (
      WidgetTester tester,
    ) async {
      // Build our app and trigger a frame.
      await tester.pumpWidget(const MyApp());
      await tester.pumpAndSettle();

      // Verify that the app title is displayed
      expect(find.text('XMTP v3 Flutter Demo'), findsOneWidget);

      // Verify that the private key text field is present
      expect(find.byType(TextField), findsWidgets);

      // Verify that the Create Client button is present
      expect(find.text('Create Client'), findsOneWidget);

      // Verify that basic UI elements are present
      expect(find.byType(TextField), findsWidgets);
      expect(find.byType(ElevatedButton), findsWidgets);
    });

    testWidgets('Private key field should be pre-filled with random key', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(const MyApp());
      await tester.pumpAndSettle();

      // Find text fields and look for the one with private key
      final textFields = find.byType(TextField);
      expect(textFields, findsWidgets);

      // Get the first text field (private key field)
      final TextField firstTextField = tester.widget(textFields.first);
      final String privateKeyText = firstTextField.controller?.text ?? '';

      // Verify that the private key is pre-filled and has correct format
      expect(privateKeyText.isNotEmpty, true);
      expect(privateKeyText.startsWith('0x'), true);
      expect(privateKeyText.length, 66); // 0x + 64 hex characters

      // Verify it's a valid hex string
      final hexPart = privateKeyText.substring(2);
      expect(RegExp(r'^[0-9a-fA-F]+$').hasMatch(hexPart), true);
    });

    testWidgets('Create Client button should be enabled initially', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(const MyApp());
      await tester.pumpAndSettle();

      final createClientButton = find.text('Create Client');
      expect(createClientButton, findsOneWidget);

      // Verify button is enabled (not disabled)
      final ElevatedButton button = tester.widget(
        find.byType(ElevatedButton).first,
      );
      expect(button.onPressed, isNotNull);
    });

    testWidgets('Environment dropdown should have correct default', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(const MyApp());
      await tester.pumpAndSettle();

      // Look for UI elements that should be present
      expect(find.byType(TextField), findsWidgets);
      expect(find.byType(ElevatedButton), findsWidgets);
    });

    testWidgets('Client info should be hidden initially', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(const MyApp());
      await tester.pumpAndSettle();

      // Client info should not be visible initially
      expect(find.text('Client Info'), findsNothing);
      expect(find.text('Inbox ID:'), findsNothing);
      expect(find.text('Installation ID:'), findsNothing);
    });

    testWidgets('Platform version should be displayed', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(const MyApp());
      await tester.pumpAndSettle();

      // Verify that platform version is retrieved and displayed
      expect(
        find.byWidgetPredicate(
          (Widget widget) =>
              widget is Text && widget.data!.startsWith('Platform:'),
        ),
        findsOneWidget,
      );
    });

    testWidgets('Error handling should work for invalid input', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(const MyApp());
      await tester.pumpAndSettle();

      // Find the private key text field and enter invalid data
      final textFields = find.byType(TextField);
      await tester.enterText(textFields.first, 'invalid_key');

      // Tap Create Client button
      final createClientButton = find.text('Create Client');
      await tester.tap(createClientButton);
      await tester.pump();

      // The app should handle the error gracefully (no crash)
      expect(tester.takeException(), isNull);
    });

    testWidgets('UI should be responsive to different screen sizes', (
      WidgetTester tester,
    ) async {
      // Test with different screen sizes
      await tester.binding.setSurfaceSize(const Size(400, 800));
      await tester.pumpWidget(const MyApp());
      await tester.pumpAndSettle();

      // Verify UI elements are still present
      expect(find.text('XMTP v3 Flutter Demo'), findsOneWidget);
      expect(find.text('Create Client'), findsOneWidget);

      // Test with smaller screen
      await tester.binding.setSurfaceSize(const Size(300, 600));
      await tester.pump();

      // UI should still be functional
      expect(find.text('XMTP v3 Flutter Demo'), findsOneWidget);
    });

    testWidgets('Multiple button taps should be handled gracefully', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(const MyApp());
      await tester.pumpAndSettle();

      final createClientButton = find.text('Create Client');

      // Tap multiple times quickly
      await tester.tap(createClientButton);
      await tester.tap(createClientButton);
      await tester.tap(createClientButton);
      await tester.pump();

      // Should handle multiple taps without crashing
      expect(tester.takeException(), isNull);
    });
  });

  group('Private Key Generation Tests', () {
    test('Generated private keys should be unique', () {
      final keys = <String>{};

      // Generate multiple keys and verify they're unique
      for (int i = 0; i < 10; i++) {
        final key = _generateTestPrivateKey();
        expect(
          keys.contains(key),
          false,
          reason: 'Generated duplicate private key',
        );
        keys.add(key);
      }
    });

    test('Generated private keys should have correct format', () {
      for (int i = 0; i < 5; i++) {
        final key = _generateTestPrivateKey();

        // Should start with 0x
        expect(key.startsWith('0x'), true);

        // Should be 66 characters total (0x + 64 hex chars)
        expect(key.length, 66);

        // Should contain only valid hex characters
        final hexPart = key.substring(2);
        expect(RegExp(r'^[0-9a-fA-F]+$').hasMatch(hexPart), true);
      }
    });

    test('Private key should not be all zeros or all ones', () {
      for (int i = 0; i < 5; i++) {
        final key = _generateTestPrivateKey();
        final hexPart = key.substring(2);

        // Should not be all zeros
        expect(hexPart, isNot('0' * 64));

        // Should not be all ones (f's)
        expect(hexPart.toLowerCase(), isNot('f' * 64));
      }
    });
  });

  group('XMTP Model Tests', () {
    test('XmtpClient model should handle data correctly', () {
      final client = XmtpClient(
        inboxId: 'test_inbox_id',
        installationId: 'test_installation_id',
        environment: 'dev',
        dbPath: '/test/db/path',
        publicIdentity: {
          'address': '0x1234567890123456789012345678901234567890',
        },
      );

      expect(client.inboxId, 'test_inbox_id');
      expect(client.installationId, 'test_installation_id');
      expect(client.environment, 'dev');
      expect(client.dbPath, '/test/db/path');
      expect(
        client.publicIdentity['address'],
        '0x1234567890123456789012345678901234567890',
      );
    });

    test('XmtpMessage model should handle data correctly', () {
      final message = XmtpMessage(
        id: 'msg_123',
        conversationId: 'conv_123',
        content: 'Hello, XMTP!',
        contentType: 'text/plain',
        senderInboxId: 'sender_inbox',
        sentAt: DateTime.now(),
        deliveryStatus: MessageDeliveryStatus.published,
      );

      expect(message.id, 'msg_123');
      expect(message.conversationId, 'conv_123');
      expect(message.content, 'Hello, XMTP!');
      expect(message.contentType, 'text/plain');
      expect(message.senderInboxId, 'sender_inbox');
      expect(message.sentAt, isA<DateTime>());
      expect(message.deliveryStatus, MessageDeliveryStatus.published);
    });

    test('Dm conversation model should handle data correctly', () {
      final dm = Dm(
        id: 'dm_123',
        topic: 'test_topic',
        createdAt: DateTime.now(),
        consentState: ConsentState.allowed,
        isActive: true,
        peerInboxId: 'peer_inbox',
        peerAddress: '0x1234567890123456789012345678901234567890',
      );

      expect(dm.id, 'dm_123');
      expect(dm.topic, 'test_topic');
      expect(dm.peerInboxId, 'peer_inbox');
      expect(dm.createdAt, isA<DateTime>());
      expect(dm.consentState, ConsentState.allowed);
      expect(dm.isActive, true);
      expect(dm.type, ConversationType.dm);
    });

    test('Group model should handle data correctly', () {
      final group = Group(
        id: 'group_123',
        name: 'Test Group',
        description: 'A test group',
        imageUrl: 'https://example.com/image.png',
        createdAt: DateTime.now(),
        memberInboxIds: ['member1', 'member2'],
        adminInboxIds: ['admin1'],
        superAdminInboxIds: ['superadmin1'],
        consentState: ConsentState.allowed,
        isActive: true,
        topic: 'group_topic',
        creatorInboxId: 'creator_inbox',
        permissions: GroupPermissions(
          addMemberPermission: PermissionOption.allow,
          removeMemberPermission: PermissionOption.adminOnly,
          updateNamePermission: PermissionOption.adminOnly,
          updateDescriptionPermission: PermissionOption.adminOnly,
          updateImagePermission: PermissionOption.adminOnly,
        ),
      );

      expect(group.id, 'group_123');
      expect(group.name, 'Test Group');
      expect(group.description, 'A test group');
      expect(group.imageUrl, 'https://example.com/image.png');
      expect(group.createdAt, isA<DateTime>());
      expect(group.memberInboxIds, ['member1', 'member2']);
      expect(group.adminInboxIds, ['admin1']);
      expect(group.superAdminInboxIds, ['superadmin1']);
      expect(group.type, ConversationType.group);
      expect(group.creatorInboxId, 'creator_inbox');
      expect(group.permissions.addMemberPermission, PermissionOption.allow);
      expect(
        group.permissions.removeMemberPermission,
        PermissionOption.adminOnly,
      );
    });

    test('ClientOptions should handle data correctly', () {
      final options = ClientOptions(
        environment: 'production',
        appVersion: '1.0.0',
        dbDirectory: '/custom/db',
        deviceSyncEnabled: false,
        debugEventsEnabled: true,
      );

      expect(options.environment, 'production');
      expect(options.appVersion, '1.0.0');
      expect(options.dbDirectory, '/custom/db');
      expect(options.deviceSyncEnabled, false);
      expect(options.debugEventsEnabled, true);
    });

    test('XmtpEnvironment enum should work correctly', () {
      expect(XmtpEnvironment.dev.value, 'dev');
      expect(XmtpEnvironment.production.value, 'production');
      expect(XmtpEnvironment.local.value, 'local');

      expect(XmtpEnvironment.fromString('dev'), XmtpEnvironment.dev);
      expect(
        XmtpEnvironment.fromString('production'),
        XmtpEnvironment.production,
      );
      expect(XmtpEnvironment.fromString('local'), XmtpEnvironment.local);
    });

    test('ConversationType enum should work correctly', () {
      expect(ConversationType.dm.value, 'dm');
      expect(ConversationType.group.value, 'group');

      expect(ConversationType.fromString('dm'), ConversationType.dm);
      expect(ConversationType.fromString('group'), ConversationType.group);
    });

    test('ConsentState enum should work correctly', () {
      expect(ConsentState.allowed.value, 'allowed');
      expect(ConsentState.denied.value, 'denied');
      expect(ConsentState.unknown.value, 'unknown');

      expect(ConsentState.fromString('allowed'), ConsentState.allowed);
      expect(ConsentState.fromString('denied'), ConsentState.denied);
      expect(ConsentState.fromString('unknown'), ConsentState.unknown);
    });
  });

  group('Error Handling Tests', () {
    test('XmtpException should handle error data correctly', () {
      final exception = XmtpException(
        'This is a test error message',
        code: 'TEST_ERROR',
        details: 'Additional details about the error',
      );

      expect(exception.code, 'TEST_ERROR');
      expect(exception.message, 'This is a test error message');
      expect(exception.details, 'Additional details about the error');
      expect(exception.toString(), contains('TEST_ERROR'));
      expect(exception.toString(), contains('This is a test error message'));
    });
  });
}

/// Helper function to generate test private keys
String _generateTestPrivateKey() {
  final random = Random();
  final bytes = List<int>.generate(32, (i) => random.nextInt(256));
  final hexString =
      bytes.map((b) => b.toRadixString(16).padLeft(2, '0')).join();
  return '0x$hexString';
}
