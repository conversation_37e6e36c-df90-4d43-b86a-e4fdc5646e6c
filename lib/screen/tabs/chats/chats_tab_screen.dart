import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:toii_mesh/core/service/auth_xmtp_bridge_service.dart';
import 'package:toii_mesh/cubit/xmtp/xmtp_cubit.dart';
import 'package:toii_mesh/cubit/xmtp/xmtp_state.dart';
import 'package:toii_mesh/model/chat/chat_models.dart';
import 'package:toii_mesh/screen/chat/chat_screen.dart';
import 'package:toii_mesh/screen/tabs/chats/widgets/chat_search_bar.dart';
import 'package:toii_mesh/screen/tabs/chats/widgets/connect_chat_dialog.dart';
import 'package:toii_mesh/screen/tabs/chats/widgets/conversation_list.dart';
import 'package:toii_mesh/screen/tabs/chats/widgets/empty_state.dart';
import 'package:toii_mesh/screen/tabs/chats/widgets/error_state.dart';
import 'package:toii_mesh/screen/tabs/chats/widgets/user_info_widget.dart';
import 'package:toii_xmtp_flutter/toii_xmtp_flutter.dart';

class ChatsTabScreen extends StatefulWidget {
  const ChatsTabScreen({super.key});

  @override
  State<ChatsTabScreen> createState() => _ChatsTabScreenState();
}

class _ChatsTabScreenState extends State<ChatsTabScreen> {
  final TextEditingController _searchController = TextEditingController();
  bool _isReloading = false;

  @override
  void initState() {
    super.initState();
    // Check if XMTP should be auto-initialized when screen loads
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkAndInitializeXmtp();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _checkAndInitializeXmtp() async {
    final xmtpCubit = context.read<XmtpCubit>();

    // Only initialize if not already initialized
    if (!xmtpCubit.hasClient) {
      final canAutoInit = await xmtpCubit.canAutoInitialize();
      if (canAutoInit) {
        await xmtpCubit.autoInitializeClient();
      }
    }
  }

  Future<void> _initializeXmtpClient() async {
    final xmtpCubit = context.read<XmtpCubit>();
    await xmtpCubit.autoInitializeClient();
  }

  Future<void> _reinitializeXmtp() async {
    try {
      await AuthXmtpBridgeService().reinitializeXmtp();
    } catch (e) {
      // Fallback to direct initialization
      await _initializeXmtpClient();
    }
  }

  Future<void> _reloadConversations() async {
    final xmtpCubit = context.read<XmtpCubit>();

    // Only reload if XMTP client is initialized
    if (!xmtpCubit.hasClient) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('XMTP client not initialized'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    setState(() {
      _isReloading = true;
    });

    try {
      await xmtpCubit.loadConversations();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Conversations reloaded successfully'),
            backgroundColor: Color(0xFF10B981),
            duration: Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to reload conversations: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isReloading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Column(
          children: [
            // User info widget (shows when XMTP is connected)
            const UserInfoWidget(),

            // Search bar
            ChatSearchBar(
              controller: _searchController,
              onAddPressed: _handleAddPressed,
              onQrPressed: _handleQrPressed,
              onReloadPressed: _reloadConversations,
              onSearchChanged: _handleSearchChanged,
              isReloading: _isReloading,
            ),

            // Chat list
            Expanded(
              child: BlocBuilder<XmtpCubit, XmtpState>(
                builder: (context, state) {
                  if (state is XmtpInitial) {
                    return ChatEmptyState(
                      onInitializePressed: _reinitializeXmtp,
                    );
                  } else if (state is XmtpLoading) {
                    return const ChatEmptyState(isLoading: true);
                  } else if (state is XmtpClientCreated) {
                    // Show loading state while conversations are being loaded
                    return const ChatEmptyState(
                      isLoading: true,
                    );
                  } else if (state is XmtpLoadingConversations) {
                    return const ChatEmptyState(
                      isLoading: true,
                    );
                  } else if (state is XmtpConversationsLoaded) {
                    return _buildConversationsList(state.conversations);
                  } else if (state is XmtpError) {
                    return ChatErrorState(
                      message: state.message,
                      onRetryPressed: _reinitializeXmtp,
                    );
                  }
                  return const ChatEmptyState();
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildConversationsList(List<Conversation> conversations) {
    // Convert XMTP conversations to ChatConversation models
    final chatConversations = conversations
        .map((conv) => ChatConversation.fromConversation(conv))
        .toList();

    return ConversationList(
      conversations: chatConversations,
      onConversationTap: _handleConversationTap,
    );
  }

  void _handleConversationTap(ChatConversation conversation) {
    final xmtpCubit = context.read<XmtpCubit>();
    final currentUserInboxId = xmtpCubit.inboxId;

    if (currentUserInboxId != null) {
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => ChatScreen(
            conversation: conversation,
            currentUserInboxId: currentUserInboxId,
            xmtpCubit: xmtpCubit,
          ),
        ),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('XMTP client not properly initialized'),
        ),
      );
    }
  }

  void _handleAddPressed() async {
    final xmtpCubit = context.read<XmtpCubit>();

    // Check if XMTP client is initialized
    if (!xmtpCubit.hasClient) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please initialize XMTP client first'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    // Show connect chat dialog
    final result = await showConnectChatDialog(
      context,
      onConnect: (inboxId) async {
        await xmtpCubit.createDm(inboxId);
      },
      currentUserInboxId: xmtpCubit.inboxId,
    );

    // Handle result if needed
    if (result == true) {
      // Conversation was created successfully
      // The dialog already shows a success message
      debugPrint('New conversation created successfully');
    }
  }

  void _handleQrPressed() {
    // TODO: Implement QR code scanner functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('QR Scanner - Coming soon!'),
      ),
    );
  }

  void _handleSearchChanged(String query) {
    // TODO: Implement search functionality
    // For now, just log the search query
    debugPrint('Search query: $query');
  }
}
