import 'dart:convert';
import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';
import 'package:toii_mesh/cubit/app_settings/app_settings_cubit.dart';
import 'package:toii_mesh/cubit/auth/login/login/login_cubit.dart';
import 'package:toii_mesh/cubit/theme/theme_cubit.dart';
import 'package:toii_mesh/gen/assets.gen.dart';
import 'package:toii_mesh/model/user/user_model.dart';
import 'package:toii_mesh/router/app_router.dart';
import 'package:toii_mesh/utils/keychain/keychain_service.dart';
import 'package:toii_mesh/widget/colors/colors.dart';
import 'package:toii_mesh/widget/colors/text_style.dart';
import 'package:toii_mesh/widget/images/avatar_widget.dart';
import 'package:toii_mesh/widget/snack_bar/snackbar.dart';
import 'package:web3dart/crypto.dart';
import 'package:web3dart/web3dart.dart';

class StartScreen extends StatefulWidget {
  const StartScreen({super.key});

  @override
  State<StartScreen> createState() => _StartScreenState();
}

class _StartScreenState extends State<StartScreen> {
  Map<String, UserModel> profileData = {};
  @override
  void initState() {
    super.initState();
    Future.microtask(() async {
      getData();
    });
  }

  void getData() async {
    try {
      final profile = await KeychainService.instance.getProfile();
      final map = profile.map(
        (key, value) => MapEntry(key, UserModel.fromJson(value)),
      );
      setState(() {
        profileData = map;
      });
    } catch (e) {
      print(e);
    }
  }

  Future<void> _loginWithWallet(BuildContext context, String walletKey) async {
    try {
      EasyLoading.show();

      // 1. Lấy private key từ keychain
      final privateKey =
          await KeychainService.instance.readPrivateKeyFromiCloud(walletKey);
      if (privateKey == null || privateKey.isEmpty) {
        EasyLoading.dismiss();
        context.showSnackbar(message: "Private key not found for this wallet");
        return;
      }

      // 2. Lấy nonce message từ server
      final loginCubit = context.read<LoginCubit>();
      final nonceMessage =
          await loginCubit.getNonceMessageForWalletSignature(walletKey);
      if (nonceMessage == null) {
        EasyLoading.dismiss();
        context.showSnackbar(message: "Failed to get nonce message");
        return;
      }

      // 3. Tạo signature bằng cách ký message với private key
      final signature = await _personalSignByPrivateKey(
        privateKeyHex: privateKey,
        message: nonceMessage,
      );

      EasyLoading.dismiss();

      // 4. Thực hiện login với signature đã tạo
      loginCubit.loginWithWallet(
        address: walletKey,
        signature: signature,
      );
    } catch (e) {
      EasyLoading.dismiss();
      context.showSnackbar(message: "Error: ${e.toString()}");
    }
  }

  Future<String> _personalSignByPrivateKey({
    required String privateKeyHex,
    required String message,
  }) async {
    final key = EthPrivateKey.fromHex(privateKeyHex);
    final msgBytes = Uint8List.fromList(utf8.encode(message));

    // web3dart applies the EIP-191 prefix internally for "personal" signing
    final sigBytes = key.signPersonalMessageToUint8List(msgBytes);

    return bytesToHex(sigBytes, include0x: true);
  }

  @override
  Widget build(BuildContext context) {
    var setting = GetIt.instance<AppSettingsCubit>().state.appSettings;
    return BlocProvider(
      create: (context) => LoginCubit(),
      child: BlocConsumer<LoginCubit, LoginState>(
        listener: (context, state) {
          if (state.status.isSuccess) {
            context.go(RouterEnums.inital.routeName);
          }
          if (state.status.isFailure) {
            context.showSnackbar(message: state.message ?? "");
          }
        },
        builder: (context, state) {
          final profileKeys = profileData.keys.toList();
          return Scaffold(
            backgroundColor: Colors.transparent,
            extendBodyBehindAppBar: true,
            body: Container(
              height: double.infinity,
              width: double.infinity,
              decoration: BoxDecoration(
                image: DecorationImage(
                  image: Assets.images.bgHome.provider(),
                  fit: BoxFit.cover,
                ),
              ),
              child: SafeArea(
                bottom: false,
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Assets.icons.newLogo.image(),
                      Expanded(
                        child: GridView.count(
                          childAspectRatio: 1 / 1.5,
                          mainAxisSpacing: 16,
                          crossAxisSpacing: 16,
                          crossAxisCount: 2,
                          children: List.generate(profileKeys.length, (index) {
                            final walletKey = profileKeys[index];
                            return Builder(
                              builder: (context) {
                                return GestureDetector(
                                  onTap: () async {
                                    await _loginWithWallet(context, walletKey);
                                  },
                                  child: Center(
                                    child: Column(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        AvatarWidget(
                                          size: 154,
                                          imageUrl:
                                              profileData[walletKey]?.avatarUrl,
                                          name: profileData[walletKey]
                                                  ?.username ??
                                              '',
                                        ),
                                        const SizedBox(height: 16),
                                        Text(
                                          profileData[walletKey]?.username ??
                                              "",
                                          style: titleMedium.copyColor(
                                            themeData.neutral800,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                );
                              },
                            );
                          }),
                        ),
                      ),
                      _nextButton(),
                    ],
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _nextButton() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        const SizedBox(height: 16),
        SizedBox(
          width: double.infinity,
          height: 82 + MediaQuery.of(context).padding.bottom,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: GestureDetector(
                  onTap: () {
                    context.push(RouterEnums.createAcount.routeName);
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 22),
                    alignment: Alignment.center,
                    height: 48,
                    decoration: BoxDecoration(
                      border: Border.all(color: themeData.primaryGreen500),
                      // color: themeData.black800,
                      borderRadius: BorderRadius.circular(100),
                    ),
                    child: Text(
                      'Activate Soul',
                      style: titleMedium.copyColor(themeData.primaryGreen500),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Builder(
                builder: (context) {
                  return GestureDetector(
                    onTap: () {},
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 22),
                      alignment: Alignment.center,
                      height: 48,
                      decoration: BoxDecoration(
                        border: Border.all(color: themeData.primaryGreen500),
                        // color: themeData.black800,
                        borderRadius: BorderRadius.circular(100),
                      ),
                      child: Text(
                        'Restore',
                        style: titleMedium.copyColor(themeData.primaryGreen500),
                      ),
                    ),
                  );
                },
              ),
            ],
          ),
        ),
      ],
    );
  }
}
