// dart format width=80

/// GENERATED CODE - DO NOT MODIFY BY HAND
/// *****************************************************
///  FlutterGen
/// *****************************************************

// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: deprecated_member_use,directives_ordering,implicit_dynamic_list_literal,unnecessary_import

import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_svg/flutter_svg.dart' as _svg;
import 'package:vector_graphics/vector_graphics.dart' as _vg;

class $AssetsAbiGen {
  const $AssetsAbiGen();

  /// File path: assets/abi/erc20tokenabi.json
  String get erc20tokenabi => 'assets/abi/erc20tokenabi.json';

  /// File path: assets/abi/usdt.abis.json
  String get usdtAbis => 'assets/abi/usdt.abis.json';

  /// List of all assets
  List<String> get values => [erc20tokenabi, usdtAbis];
}

class $AssetsDefibrowserGen {
  const $AssetsDefibrowserGen();

  /// File path: assets/defibrowser/posi.min.js
  String get posiMin => 'assets/defibrowser/posi.min.js';

  /// List of all assets
  List<String> get values => [posiMin];
}

class $AssetsIconsGen {
  const $AssetsIconsGen();

  /// File path: assets/icons/bi_emoji-smile.svg
  SvgGenImage get biEmojiSmile =>
      const SvgGenImage('assets/icons/bi_emoji-smile.svg');

  /// File path: assets/icons/eye_hide.svg
  SvgGenImage get eyeHide => const SvgGenImage('assets/icons/eye_hide.svg');

  /// File path: assets/icons/eye_show.svg
  SvgGenImage get eyeShow => const SvgGenImage('assets/icons/eye_show.svg');

  /// File path: assets/icons/ic_apple.svg
  SvgGenImage get icApple => const SvgGenImage('assets/icons/ic_apple.svg');

  /// File path: assets/icons/ic_arrow_down.svg
  SvgGenImage get icArrowDown =>
      const SvgGenImage('assets/icons/ic_arrow_down.svg');

  /// File path: assets/icons/ic_arrow_onboarding.svg
  SvgGenImage get icArrowOnboarding =>
      const SvgGenImage('assets/icons/ic_arrow_onboarding.svg');

  /// File path: assets/icons/ic_arrow_right.svg
  SvgGenImage get icArrowRight =>
      const SvgGenImage('assets/icons/ic_arrow_right.svg');

  /// File path: assets/icons/ic_arrow_right_account.svg
  SvgGenImage get icArrowRightAccount =>
      const SvgGenImage('assets/icons/ic_arrow_right_account.svg');

  /// File path: assets/icons/ic_arrow_right_figma.svg
  SvgGenImage get icArrowRightFigma =>
      const SvgGenImage('assets/icons/ic_arrow_right_figma.svg');

  /// File path: assets/icons/ic_back.svg
  SvgGenImage get icBack => const SvgGenImage('assets/icons/ic_back.svg');

  /// File path: assets/icons/ic_binance.png
  AssetGenImage get icBinance =>
      const AssetGenImage('assets/icons/ic_binance.png');

  /// File path: assets/icons/ic_block.svg
  SvgGenImage get icBlock => const SvgGenImage('assets/icons/ic_block.svg');

  /// File path: assets/icons/ic_calendar.svg
  SvgGenImage get icCalendar =>
      const SvgGenImage('assets/icons/ic_calendar.svg');

  /// File path: assets/icons/ic_camera.svg
  SvgGenImage get icCamera => const SvgGenImage('assets/icons/ic_camera.svg');

  /// File path: assets/icons/ic_checkbox.svg
  SvgGenImage get icCheckbox =>
      const SvgGenImage('assets/icons/ic_checkbox.svg');

  /// File path: assets/icons/ic_checked.svg
  SvgGenImage get icChecked => const SvgGenImage('assets/icons/ic_checked.svg');

  /// File path: assets/icons/ic_close_circle.svg
  SvgGenImage get icCloseCircle =>
      const SvgGenImage('assets/icons/ic_close_circle.svg');

  /// File path: assets/icons/ic_close_square.svg
  SvgGenImage get icCloseSquare =>
      const SvgGenImage('assets/icons/ic_close_square.svg');

  /// File path: assets/icons/ic_comment.svg
  SvgGenImage get icComment => const SvgGenImage('assets/icons/ic_comment.svg');

  /// File path: assets/icons/ic_comment_1.png
  AssetGenImage get icComment1 =>
      const AssetGenImage('assets/icons/ic_comment_1.png');

  /// File path: assets/icons/ic_copy.svg
  SvgGenImage get icCopy => const SvgGenImage('assets/icons/ic_copy.svg');

  /// File path: assets/icons/ic_dapp.svg
  SvgGenImage get icDapp => const SvgGenImage('assets/icons/ic_dapp.svg');

  /// File path: assets/icons/ic_discord.svg
  SvgGenImage get icDiscord => const SvgGenImage('assets/icons/ic_discord.svg');

  /// File path: assets/icons/ic_dropdown.svg
  SvgGenImage get icDropdown =>
      const SvgGenImage('assets/icons/ic_dropdown.svg');

  /// File path: assets/icons/ic_earn.svg
  SvgGenImage get icEarn => const SvgGenImage('assets/icons/ic_earn.svg');

  /// File path: assets/icons/ic_edit.svg
  SvgGenImage get icEdit => const SvgGenImage('assets/icons/ic_edit.svg');

  /// File path: assets/icons/ic_edit_background.svg
  SvgGenImage get icEditBackground =>
      const SvgGenImage('assets/icons/ic_edit_background.svg');

  /// File path: assets/icons/ic_edit_profile.svg
  SvgGenImage get icEditProfile =>
      const SvgGenImage('assets/icons/ic_edit_profile.svg');

  /// File path: assets/icons/ic_empty_item.svg
  SvgGenImage get icEmptyItem =>
      const SvgGenImage('assets/icons/ic_empty_item.svg');

  /// File path: assets/icons/ic_faceId.svg
  SvgGenImage get icFaceId => const SvgGenImage('assets/icons/ic_faceId.svg');

  /// File path: assets/icons/ic_gao.svg
  SvgGenImage get icGao => const SvgGenImage('assets/icons/ic_gao.svg');

  /// File path: assets/icons/ic_gao_travel.png
  AssetGenImage get icGaoTravel =>
      const AssetGenImage('assets/icons/ic_gao_travel.png');

  /// File path: assets/icons/ic_gen_ai.svg
  SvgGenImage get icGenAi => const SvgGenImage('assets/icons/ic_gen_ai.svg');

  /// File path: assets/icons/ic_global.svg
  SvgGenImage get icGlobal => const SvgGenImage('assets/icons/ic_global.svg');

  /// File path: assets/icons/ic_gmail.svg
  SvgGenImage get icGmail => const SvgGenImage('assets/icons/ic_gmail.svg');

  /// File path: assets/icons/ic_heart.svg
  SvgGenImage get icHeart => const SvgGenImage('assets/icons/ic_heart.svg');

  /// File path: assets/icons/ic_heart_filled.svg
  SvgGenImage get icHeartFilled =>
      const SvgGenImage('assets/icons/ic_heart_filled.svg');

  /// File path: assets/icons/ic_hide_eye.svg
  SvgGenImage get icHideEye =>
      const SvgGenImage('assets/icons/ic_hide_eye.svg');

  /// File path: assets/icons/ic_history_empty.svg
  SvgGenImage get icHistoryEmpty =>
      const SvgGenImage('assets/icons/ic_history_empty.svg');

  /// File path: assets/icons/ic_home.png
  AssetGenImage get icHome => const AssetGenImage('assets/icons/ic_home.png');

  /// File path: assets/icons/ic_home_ai.png
  AssetGenImage get icHomeAi =>
      const AssetGenImage('assets/icons/ic_home_ai.png');

  /// File path: assets/icons/ic_home_test.svg
  SvgGenImage get icHomeTest =>
      const SvgGenImage('assets/icons/ic_home_test.svg');

  /// File path: assets/icons/ic_ibox.svg
  SvgGenImage get icIbox => const SvgGenImage('assets/icons/ic_ibox.svg');

  /// File path: assets/icons/ic_info.svg
  SvgGenImage get icInfo => const SvgGenImage('assets/icons/ic_info.svg');

  /// File path: assets/icons/ic_language_square.svg
  SvgGenImage get icLanguageSquare =>
      const SvgGenImage('assets/icons/ic_language_square.svg');

  /// File path: assets/icons/ic_location.svg
  SvgGenImage get icLocation =>
      const SvgGenImage('assets/icons/ic_location.svg');

  /// File path: assets/icons/ic_lock.svg
  SvgGenImage get icLock => const SvgGenImage('assets/icons/ic_lock.svg');

  /// File path: assets/icons/ic_lock2.svg
  SvgGenImage get icLock2Svg => const SvgGenImage('assets/icons/ic_lock2.svg');

  /// File path: assets/icons/ic_lock_2.svg
  SvgGenImage get icLock2Svg_ =>
      const SvgGenImage('assets/icons/ic_lock_2.svg');

  /// File path: assets/icons/ic_logo_home.svg
  SvgGenImage get icLogoHome =>
      const SvgGenImage('assets/icons/ic_logo_home.svg');

  /// File path: assets/icons/ic_logout.svg
  SvgGenImage get icLogout => const SvgGenImage('assets/icons/ic_logout.svg');

  /// File path: assets/icons/ic_luna_travel.svg
  SvgGenImage get icLunaTravel =>
      const SvgGenImage('assets/icons/ic_luna_travel.svg');

  /// File path: assets/icons/ic_marketplace_item1.png
  AssetGenImage get icMarketplaceItem1 =>
      const AssetGenImage('assets/icons/ic_marketplace_item1.png');

  /// File path: assets/icons/ic_marketplace_item2.png
  AssetGenImage get icMarketplaceItem2 =>
      const AssetGenImage('assets/icons/ic_marketplace_item2.png');

  /// File path: assets/icons/ic_marketplace_item3.png
  AssetGenImage get icMarketplaceItem3 =>
      const AssetGenImage('assets/icons/ic_marketplace_item3.png');

  /// File path: assets/icons/ic_metamask.png
  AssetGenImage get icMetamask =>
      const AssetGenImage('assets/icons/ic_metamask.png');

  /// File path: assets/icons/ic_more.svg
  SvgGenImage get icMore => const SvgGenImage('assets/icons/ic_more.svg');

  /// File path: assets/icons/ic_music.svg
  SvgGenImage get icMusic => const SvgGenImage('assets/icons/ic_music.svg');

  /// File path: assets/icons/ic_new_gao.png
  AssetGenImage get icNewGao =>
      const AssetGenImage('assets/icons/ic_new_gao.png');

  /// File path: assets/icons/ic_nft_passport.svg
  SvgGenImage get icNftPassport =>
      const SvgGenImage('assets/icons/ic_nft_passport.svg');

  /// File path: assets/icons/ic_note.svg
  SvgGenImage get icNote => const SvgGenImage('assets/icons/ic_note.svg');

  /// File path: assets/icons/ic_noti_home.svg
  SvgGenImage get icNotiHome =>
      const SvgGenImage('assets/icons/ic_noti_home.svg');

  /// File path: assets/icons/ic_notification.svg
  SvgGenImage get icNotification =>
      const SvgGenImage('assets/icons/ic_notification.svg');

  /// File path: assets/icons/ic_notification_bing.svg
  SvgGenImage get icNotificationBing =>
      const SvgGenImage('assets/icons/ic_notification_bing.svg');

  /// File path: assets/icons/ic_opensea.png
  AssetGenImage get icOpensea =>
      const AssetGenImage('assets/icons/ic_opensea.png');

  /// File path: assets/icons/ic_pay_network.svg
  SvgGenImage get icPayNetwork =>
      const SvgGenImage('assets/icons/ic_pay_network.svg');

  /// File path: assets/icons/ic_picture.svg
  SvgGenImage get icPicture => const SvgGenImage('assets/icons/ic_picture.svg');

  /// File path: assets/icons/ic_profile.svg
  SvgGenImage get icProfile => const SvgGenImage('assets/icons/ic_profile.svg');

  /// File path: assets/icons/ic_profile_2user.svg
  SvgGenImage get icProfile2user =>
      const SvgGenImage('assets/icons/ic_profile_2user.svg');

  /// File path: assets/icons/ic_receive.svg
  SvgGenImage get icReceive => const SvgGenImage('assets/icons/ic_receive.svg');

  /// File path: assets/icons/ic_reply.svg
  SvgGenImage get icReply => const SvgGenImage('assets/icons/ic_reply.svg');

  /// File path: assets/icons/ic_repost.svg
  SvgGenImage get icRepost => const SvgGenImage('assets/icons/ic_repost.svg');

  /// File path: assets/icons/ic_repost_1.png
  AssetGenImage get icRepost1 =>
      const AssetGenImage('assets/icons/ic_repost_1.png');

  /// File path: assets/icons/ic_responses.svg
  SvgGenImage get icResponses =>
      const SvgGenImage('assets/icons/ic_responses.svg');

  /// File path: assets/icons/ic_runx.png
  AssetGenImage get icRunxPng =>
      const AssetGenImage('assets/icons/ic_runx.png');

  /// File path: assets/icons/ic_runx.svg
  SvgGenImage get icRunxSvg => const SvgGenImage('assets/icons/ic_runx.svg');

  /// File path: assets/icons/ic_save_add.svg
  SvgGenImage get icSaveAdd =>
      const SvgGenImage('assets/icons/ic_save_add.svg');

  /// File path: assets/icons/ic_scan_qr.svg
  SvgGenImage get icScanQr => const SvgGenImage('assets/icons/ic_scan_qr.svg');

  /// File path: assets/icons/ic_scroll_down.svg
  SvgGenImage get icScrollDown =>
      const SvgGenImage('assets/icons/ic_scroll_down.svg');

  /// File path: assets/icons/ic_scroll_up.svg
  SvgGenImage get icScrollUp =>
      const SvgGenImage('assets/icons/ic_scroll_up.svg');

  /// File path: assets/icons/ic_search.svg
  SvgGenImage get icSearch => const SvgGenImage('assets/icons/ic_search.svg');

  /// File path: assets/icons/ic_send.svg
  SvgGenImage get icSend => const SvgGenImage('assets/icons/ic_send.svg');

  /// File path: assets/icons/ic_send_ai.svg
  SvgGenImage get icSendAi => const SvgGenImage('assets/icons/ic_send_ai.svg');

  /// File path: assets/icons/ic_setting.svg
  SvgGenImage get icSetting => const SvgGenImage('assets/icons/ic_setting.svg');

  /// File path: assets/icons/ic_share.svg
  SvgGenImage get icShare => const SvgGenImage('assets/icons/ic_share.svg');

  /// File path: assets/icons/ic_share_1.svg
  SvgGenImage get icShare1 => const SvgGenImage('assets/icons/ic_share_1.svg');

  /// File path: assets/icons/ic_share_2.svg
  SvgGenImage get icShare2 => const SvgGenImage('assets/icons/ic_share_2.svg');

  /// File path: assets/icons/ic_shield_security.svg
  SvgGenImage get icShieldSecurity =>
      const SvgGenImage('assets/icons/ic_shield_security.svg');

  /// File path: assets/icons/ic_show_eye.svg
  SvgGenImage get icShowEye =>
      const SvgGenImage('assets/icons/ic_show_eye.svg');

  /// File path: assets/icons/ic_sms.svg
  SvgGenImage get icSms => const SvgGenImage('assets/icons/ic_sms.svg');

  /// File path: assets/icons/ic_swap.svg
  SvgGenImage get icSwap => const SvgGenImage('assets/icons/ic_swap.svg');

  /// File path: assets/icons/ic_tabbar_add.svg
  SvgGenImage get icTabbarAdd =>
      const SvgGenImage('assets/icons/ic_tabbar_add.svg');

  /// File path: assets/icons/ic_tabbar_discover.svg
  SvgGenImage get icTabbarDiscover =>
      const SvgGenImage('assets/icons/ic_tabbar_discover.svg');

  /// File path: assets/icons/ic_tabbar_explore.svg
  SvgGenImage get icTabbarExplore =>
      const SvgGenImage('assets/icons/ic_tabbar_explore.svg');

  /// File path: assets/icons/ic_tabbar_explore_active.svg
  SvgGenImage get icTabbarExploreActive =>
      const SvgGenImage('assets/icons/ic_tabbar_explore_active.svg');

  /// File path: assets/icons/ic_tabbar_home.svg
  SvgGenImage get icTabbarHome =>
      const SvgGenImage('assets/icons/ic_tabbar_home.svg');

  /// File path: assets/icons/ic_tabbar_home_active.svg
  SvgGenImage get icTabbarHomeActive =>
      const SvgGenImage('assets/icons/ic_tabbar_home_active.svg');

  /// File path: assets/icons/ic_tabbar_message.svg
  SvgGenImage get icTabbarMessage =>
      const SvgGenImage('assets/icons/ic_tabbar_message.svg');

  /// File path: assets/icons/ic_tabbar_post.svg
  SvgGenImage get icTabbarPost =>
      const SvgGenImage('assets/icons/ic_tabbar_post.svg');

  /// File path: assets/icons/ic_tabbar_profile.svg
  SvgGenImage get icTabbarProfile =>
      const SvgGenImage('assets/icons/ic_tabbar_profile.svg');

  /// File path: assets/icons/ic_tabbar_profile_active.svg
  SvgGenImage get icTabbarProfileActive =>
      const SvgGenImage('assets/icons/ic_tabbar_profile_active.svg');

  /// File path: assets/icons/ic_tabbar_wallet.svg
  SvgGenImage get icTabbarWallet =>
      const SvgGenImage('assets/icons/ic_tabbar_wallet.svg');

  /// File path: assets/icons/ic_tabbar_wallet_active.svg
  SvgGenImage get icTabbarWalletActive =>
      const SvgGenImage('assets/icons/ic_tabbar_wallet_active.svg');

  /// File path: assets/icons/ic_theme.svg
  SvgGenImage get icTheme => const SvgGenImage('assets/icons/ic_theme.svg');

  /// File path: assets/icons/ic_toii.svg
  SvgGenImage get icToii => const SvgGenImage('assets/icons/ic_toii.svg');

  /// File path: assets/icons/ic_toii_ai.svg
  SvgGenImage get icToiiAi => const SvgGenImage('assets/icons/ic_toii_ai.svg');

  /// File path: assets/icons/ic_toii_network.png
  AssetGenImage get icToiiNetwork =>
      const AssetGenImage('assets/icons/ic_toii_network.png');

  /// File path: assets/icons/ic_token_logo.png
  AssetGenImage get icTokenLogo =>
      const AssetGenImage('assets/icons/ic_token_logo.png');

  /// File path: assets/icons/ic_trash.svg
  SvgGenImage get icTrash => const SvgGenImage('assets/icons/ic_trash.svg');

  /// File path: assets/icons/ic_trust_wallet.png
  AssetGenImage get icTrustWallet =>
      const AssetGenImage('assets/icons/ic_trust_wallet.png');

  /// File path: assets/icons/ic_verified.svg
  SvgGenImage get icVerified =>
      const SvgGenImage('assets/icons/ic_verified.svg');

  /// File path: assets/icons/ic_view.svg
  SvgGenImage get icView => const SvgGenImage('assets/icons/ic_view.svg');

  /// File path: assets/icons/ic_warning.svg
  SvgGenImage get icWarning => const SvgGenImage('assets/icons/ic_warning.svg');

  /// File path: assets/icons/new_logo.png
  AssetGenImage get newLogo => const AssetGenImage('assets/icons/new_logo.png');

  /// List of all assets
  List<dynamic> get values => [
        biEmojiSmile,
        eyeHide,
        eyeShow,
        icApple,
        icArrowDown,
        icArrowOnboarding,
        icArrowRight,
        icArrowRightAccount,
        icArrowRightFigma,
        icBack,
        icBinance,
        icBlock,
        icCalendar,
        icCamera,
        icCheckbox,
        icChecked,
        icCloseCircle,
        icCloseSquare,
        icComment,
        icComment1,
        icCopy,
        icDapp,
        icDiscord,
        icDropdown,
        icEarn,
        icEdit,
        icEditBackground,
        icEditProfile,
        icEmptyItem,
        icFaceId,
        icGao,
        icGaoTravel,
        icGenAi,
        icGlobal,
        icGmail,
        icHeart,
        icHeartFilled,
        icHideEye,
        icHistoryEmpty,
        icHome,
        icHomeAi,
        icHomeTest,
        icIbox,
        icInfo,
        icLanguageSquare,
        icLocation,
        icLock,
        icLock2Svg,
        icLock2Svg_,
        icLogoHome,
        icLogout,
        icLunaTravel,
        icMarketplaceItem1,
        icMarketplaceItem2,
        icMarketplaceItem3,
        icMetamask,
        icMore,
        icMusic,
        icNewGao,
        icNftPassport,
        icNote,
        icNotiHome,
        icNotification,
        icNotificationBing,
        icOpensea,
        icPayNetwork,
        icPicture,
        icProfile,
        icProfile2user,
        icReceive,
        icReply,
        icRepost,
        icRepost1,
        icResponses,
        icRunxPng,
        icRunxSvg,
        icSaveAdd,
        icScanQr,
        icScrollDown,
        icScrollUp,
        icSearch,
        icSend,
        icSendAi,
        icSetting,
        icShare,
        icShare1,
        icShare2,
        icShieldSecurity,
        icShowEye,
        icSms,
        icSwap,
        icTabbarAdd,
        icTabbarDiscover,
        icTabbarExplore,
        icTabbarExploreActive,
        icTabbarHome,
        icTabbarHomeActive,
        icTabbarMessage,
        icTabbarPost,
        icTabbarProfile,
        icTabbarProfileActive,
        icTabbarWallet,
        icTabbarWalletActive,
        icTheme,
        icToii,
        icToiiAi,
        icToiiNetwork,
        icTokenLogo,
        icTrash,
        icTrustWallet,
        icVerified,
        icView,
        icWarning,
        newLogo
      ];
}

class $AssetsImagesGen {
  const $AssetsImagesGen();

  /// File path: assets/images/avatar_sample.png
  AssetGenImage get avatarSample =>
      const AssetGenImage('assets/images/avatar_sample.png');

  /// File path: assets/images/bg_blur.png
  AssetGenImage get bgBlur => const AssetGenImage('assets/images/bg_blur.png');

  /// File path: assets/images/bg_home.png
  AssetGenImage get bgHome => const AssetGenImage('assets/images/bg_home.png');

  /// File path: assets/images/bg_loading_login.png
  AssetGenImage get bgLoadingLogin =>
      const AssetGenImage('assets/images/bg_loading_login.png');

  /// File path: assets/images/bg_login.png
  AssetGenImage get bgLogin =>
      const AssetGenImage('assets/images/bg_login.png');

  /// File path: assets/images/bg_wallet.svg
  SvgGenImage get bgWallet => const SvgGenImage('assets/images/bg_wallet.svg');

  /// File path: assets/images/bg_wallet1.png
  AssetGenImage get bgWallet1 =>
      const AssetGenImage('assets/images/bg_wallet1.png');

  /// File path: assets/images/default_background.png
  AssetGenImage get defaultBackground =>
      const AssetGenImage('assets/images/default_background.png');

  /// File path: assets/images/home_sample1.png
  AssetGenImage get homeSample1 =>
      const AssetGenImage('assets/images/home_sample1.png');

  /// File path: assets/images/img_gao_term.png
  AssetGenImage get imgGaoTerm =>
      const AssetGenImage('assets/images/img_gao_term.png');

  /// File path: assets/images/img_runx.png
  AssetGenImage get imgRunx =>
      const AssetGenImage('assets/images/img_runx.png');

  /// File path: assets/images/img_toii.png
  AssetGenImage get imgToii =>
      const AssetGenImage('assets/images/img_toii.png');

  /// File path: assets/images/img_travel.png
  AssetGenImage get imgTravel =>
      const AssetGenImage('assets/images/img_travel.png');

  /// File path: assets/images/nft_sample.png
  AssetGenImage get nftSample =>
      const AssetGenImage('assets/images/nft_sample.png');

  /// File path: assets/images/onboarding_1.png
  AssetGenImage get onboarding1 =>
      const AssetGenImage('assets/images/onboarding_1.png');

  /// File path: assets/images/onboarding_2.png
  AssetGenImage get onboarding2 =>
      const AssetGenImage('assets/images/onboarding_2.png');

  /// File path: assets/images/onboarding_3.png
  AssetGenImage get onboarding3 =>
      const AssetGenImage('assets/images/onboarding_3.png');

  /// File path: assets/images/welcome.png
  AssetGenImage get welcome => const AssetGenImage('assets/images/welcome.png');

  /// List of all assets
  List<dynamic> get values => [
        avatarSample,
        bgBlur,
        bgHome,
        bgLoadingLogin,
        bgLogin,
        bgWallet,
        bgWallet1,
        defaultBackground,
        homeSample1,
        imgGaoTerm,
        imgRunx,
        imgToii,
        imgTravel,
        nftSample,
        onboarding1,
        onboarding2,
        onboarding3,
        welcome
      ];
}

class $AssetsJsonGen {
  const $AssetsJsonGen();

  /// File path: assets/json/chainIcon.json
  String get chainIcon => 'assets/json/chainIcon.json';

  /// File path: assets/json/chainlist.json
  String get chainlist => 'assets/json/chainlist.json';

  /// File path: assets/json/defaultTokens.json
  String get defaultTokens => 'assets/json/defaultTokens.json';

  /// File path: assets/json/login.lottie
  String get login => 'assets/json/login.lottie';

  /// File path: assets/json/processing.json
  String get processing => 'assets/json/processing.json';

  /// File path: assets/json/topchain.json
  String get topchain => 'assets/json/topchain.json';

  /// List of all assets
  List<String> get values =>
      [chainIcon, chainlist, defaultTokens, login, processing, topchain];
}

class Assets {
  const Assets._();

  static const $AssetsAbiGen abi = $AssetsAbiGen();
  static const $AssetsDefibrowserGen defibrowser = $AssetsDefibrowserGen();
  static const $AssetsIconsGen icons = $AssetsIconsGen();
  static const $AssetsImagesGen images = $AssetsImagesGen();
  static const $AssetsJsonGen json = $AssetsJsonGen();
}

class AssetGenImage {
  const AssetGenImage(
    this._assetName, {
    this.size,
    this.flavors = const {},
    this.animation,
  });

  final String _assetName;

  final Size? size;
  final Set<String> flavors;
  final AssetGenImageAnimation? animation;

  Image image({
    Key? key,
    AssetBundle? bundle,
    ImageFrameBuilder? frameBuilder,
    ImageErrorWidgetBuilder? errorBuilder,
    String? semanticLabel,
    bool excludeFromSemantics = false,
    double? scale,
    double? width,
    double? height,
    Color? color,
    Animation<double>? opacity,
    BlendMode? colorBlendMode,
    BoxFit? fit,
    AlignmentGeometry alignment = Alignment.center,
    ImageRepeat repeat = ImageRepeat.noRepeat,
    Rect? centerSlice,
    bool matchTextDirection = false,
    bool gaplessPlayback = true,
    bool isAntiAlias = false,
    String? package,
    FilterQuality filterQuality = FilterQuality.medium,
    int? cacheWidth,
    int? cacheHeight,
  }) {
    return Image.asset(
      _assetName,
      key: key,
      bundle: bundle,
      frameBuilder: frameBuilder,
      errorBuilder: errorBuilder,
      semanticLabel: semanticLabel,
      excludeFromSemantics: excludeFromSemantics,
      scale: scale,
      width: width,
      height: height,
      color: color,
      opacity: opacity,
      colorBlendMode: colorBlendMode,
      fit: fit,
      alignment: alignment,
      repeat: repeat,
      centerSlice: centerSlice,
      matchTextDirection: matchTextDirection,
      gaplessPlayback: gaplessPlayback,
      isAntiAlias: isAntiAlias,
      package: package,
      filterQuality: filterQuality,
      cacheWidth: cacheWidth,
      cacheHeight: cacheHeight,
    );
  }

  ImageProvider provider({
    AssetBundle? bundle,
    String? package,
  }) {
    return AssetImage(
      _assetName,
      bundle: bundle,
      package: package,
    );
  }

  String get path => _assetName;

  String get keyName => _assetName;
}

class AssetGenImageAnimation {
  const AssetGenImageAnimation({
    required this.isAnimation,
    required this.duration,
    required this.frames,
  });

  final bool isAnimation;
  final Duration duration;
  final int frames;
}

class SvgGenImage {
  const SvgGenImage(
    this._assetName, {
    this.size,
    this.flavors = const {},
  }) : _isVecFormat = false;

  const SvgGenImage.vec(
    this._assetName, {
    this.size,
    this.flavors = const {},
  }) : _isVecFormat = true;

  final String _assetName;
  final Size? size;
  final Set<String> flavors;
  final bool _isVecFormat;

  _svg.SvgPicture svg({
    Key? key,
    bool matchTextDirection = false,
    AssetBundle? bundle,
    String? package,
    double? width,
    double? height,
    BoxFit fit = BoxFit.contain,
    AlignmentGeometry alignment = Alignment.center,
    bool allowDrawingOutsideViewBox = false,
    WidgetBuilder? placeholderBuilder,
    String? semanticsLabel,
    bool excludeFromSemantics = false,
    _svg.SvgTheme? theme,
    _svg.ColorMapper? colorMapper,
    ColorFilter? colorFilter,
    Clip clipBehavior = Clip.hardEdge,
    @deprecated Color? color,
    @deprecated BlendMode colorBlendMode = BlendMode.srcIn,
    @deprecated bool cacheColorFilter = false,
  }) {
    final _svg.BytesLoader loader;
    if (_isVecFormat) {
      loader = _vg.AssetBytesLoader(
        _assetName,
        assetBundle: bundle,
        packageName: package,
      );
    } else {
      loader = _svg.SvgAssetLoader(
        _assetName,
        assetBundle: bundle,
        packageName: package,
        theme: theme,
        colorMapper: colorMapper,
      );
    }
    return _svg.SvgPicture(
      loader,
      key: key,
      matchTextDirection: matchTextDirection,
      width: width,
      height: height,
      fit: fit,
      alignment: alignment,
      allowDrawingOutsideViewBox: allowDrawingOutsideViewBox,
      placeholderBuilder: placeholderBuilder,
      semanticsLabel: semanticsLabel,
      excludeFromSemantics: excludeFromSemantics,
      colorFilter: colorFilter ??
          (color == null ? null : ColorFilter.mode(color, colorBlendMode)),
      clipBehavior: clipBehavior,
      cacheColorFilter: cacheColorFilter,
    );
  }

  String get path => _assetName;

  String get keyName => _assetName;
}
