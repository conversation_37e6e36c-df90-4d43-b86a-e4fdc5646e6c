import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:toii_mesh/core/service/wallet_auth_service.dart';
import 'package:toii_xmtp_flutter/toii_xmtp_flutter.dart';

import 'xmtp_state.dart';

class XmtpCubit extends Cubit<XmtpState> {
  final ToiiXmtpFlutter _xmtpPlugin;
  final WalletAuthService _walletAuthService;
  XmtpClient? _client;
  StreamSubscription<Conversation>? _conversationStreamSubscription;

  XmtpCubit({
    ToiiXmtpFlutter? xmtpPlugin,
    WalletAuthService? walletAuthService,
  })  : _xmtpPlugin = xmtpPlugin ?? ToiiXmtpFlutter(),
        _walletAuthService = walletAuthService ?? WalletAuthService(),
        super(const XmtpInitial());

  XmtpClient? get client => _client;
  bool get hasClient => _client != null;
  String? get inboxId => _client?.inboxId;

  /// Create XMTP client
  Future<void> createClient({
    required String privateKey,
    String environment = 'dev',
    bool deviceSyncEnabled = true,
    bool debugEventsEnabled = true,
  }) async {
    try {
      emit(const XmtpLoading());

      final options = ClientOptions(
        environment: environment,
        deviceSyncEnabled: deviceSyncEnabled,
        debugEventsEnabled: debugEventsEnabled,
      );

      final client = await _xmtpPlugin.createClient(
        privateKey: privateKey,
        options: options,
      );

      _client = client;

      emit(XmtpClientCreated(client: client));

      // Load conversations after client creation
      await loadConversations();

      // Start conversation streaming
      await _startConversationStreaming();
    } catch (e) {
      emit(XmtpError(message: 'Failed to create client: $e'));
    }
  }

  /// Auto-initialize XMTP client using authenticated wallet
  Future<void> autoInitializeClient({
    String environment = 'dev',
    bool deviceSyncEnabled = true,
    bool debugEventsEnabled = true,
  }) async {
    try {
      emit(const XmtpLoading());

      // Get current user's wallet credentials
      final credentials =
          await _walletAuthService.getCurrentWalletCredentials();

      if (credentials == null) {
        emit(const XmtpError(
          message:
              'No wallet credentials found. Please ensure you are logged in with a wallet.',
        ));
        return;
      }

      debugPrint(
          'XmtpCubit: Auto-initializing with wallet: ${credentials.address}');

      // Create client with user's private key
      await createClient(
        privateKey: credentials.privateKey,
        environment: environment,
        deviceSyncEnabled: deviceSyncEnabled,
        debugEventsEnabled: debugEventsEnabled,
      );
    } catch (e) {
      emit(XmtpError(message: 'Failed to auto-initialize XMTP client: $e'));
    }
  }

  /// Check if auto-initialization is possible
  Future<bool> canAutoInitialize() async {
    try {
      return await _walletAuthService.hasValidWalletCredentials();
    } catch (e) {
      debugPrint('XmtpCubit: Error checking auto-initialization: $e');
      return false;
    }
  }

  /// Load conversations
  Future<void> loadConversations() async {
    if (_client == null) {
      emit(const XmtpError(message: 'Client not initialized'));
      return;
    }

    try {
      emit(const XmtpLoadingConversations());

      // Sync conversations first
      await _xmtpPlugin.syncConversations();

      // Load all conversations
      final conversations = await _xmtpPlugin.listConversations(
        includeGroups: true,
        includeDms: true,
      );

      emit(XmtpConversationsLoaded(
        client: _client!,
        conversations: conversations,
      ));
    } catch (e) {
      emit(XmtpError(message: 'Failed to load conversations: $e'));
    }
  }

  /// Create a new DM conversation
  Future<void> createDm(String targetInboxId) async {
    if (_client == null) {
      emit(const XmtpError(message: 'Client not initialized'));
      return;
    }

    try {
      emit(const XmtpCreatingConversation());

      final dm = await _xmtpPlugin.findOrCreateDm(targetInboxId);

      // Reload conversations to include the new DM
      await loadConversations();

      emit(XmtpConversationCreated(conversation: dm));
    } catch (e) {
      emit(XmtpError(message: 'Failed to create DM: $e'));
    }
  }

  /// Create a new group conversation
  Future<void> createGroup({
    required List<String> memberInboxIds,
    String? groupName,
    String? groupDescription,
    String? groupImageUrl,
  }) async {
    if (_client == null) {
      emit(const XmtpError(message: 'Client not initialized'));
      return;
    }

    try {
      emit(const XmtpCreatingConversation());

      final group = await _xmtpPlugin.createGroup(
        memberInboxIds: memberInboxIds,
        groupName: groupName,
        groupDescription: groupDescription,
        groupImageUrl: groupImageUrl,
      );

      // Reload conversations to include the new group
      await loadConversations();

      emit(XmtpConversationCreated(conversation: group));
    } catch (e) {
      emit(XmtpError(message: 'Failed to create group: $e'));
    }
  }

  /// Start conversation streaming
  Future<void> _startConversationStreaming() async {
    try {
      await _stopConversationStreaming();

      final conversationStream = _xmtpPlugin.streamConversations(
        includeGroups: true,
        includeDms: true,
      );

      _conversationStreamSubscription = conversationStream.listen(
        (conversation) => _handleNewConversation(conversation),
        onError: (error) {
          debugPrint('Conversation streaming error: $error');
          // Try to restart streaming after a delay on error
          Future.delayed(const Duration(seconds: 5), () {
            if (!isClosed && _client != null) {
              _startConversationStreaming();
            }
          });
        },
        cancelOnError: false,
      );
    } catch (e) {
      debugPrint('Failed to start conversation streaming: $e');
    }
  }

  /// Handle new conversation from stream
  void _handleNewConversation(Conversation conversation) {
    final currentState = state;
    if (currentState is XmtpConversationsLoaded) {
      // Check if conversation already exists
      final existingConversation = currentState.conversations.any(
        (conv) => conv.id == conversation.id,
      );

      if (!existingConversation) {
        final updatedConversations = [
          conversation,
          ...currentState.conversations
        ];
        emit(XmtpConversationsLoaded(
          client: currentState.client,
          conversations: updatedConversations,
        ));
      }
    }
  }

  /// Stop conversation streaming
  Future<void> _stopConversationStreaming() async {
    await _conversationStreamSubscription?.cancel();
    _conversationStreamSubscription = null;
  }

  /// Refresh conversations
  Future<void> refreshConversations() async {
    await loadConversations();
  }

  /// Disconnect client
  Future<void> disconnect() async {
    await _stopConversationStreaming();
    _client = null;
    emit(const XmtpInitial());
  }

  @override
  Future<void> close() async {
    await _stopConversationStreaming();
    return super.close();
  }
}
