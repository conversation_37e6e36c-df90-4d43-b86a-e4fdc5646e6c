import 'package:equatable/equatable.dart';
import 'package:toii_xmtp_flutter/toii_xmtp_flutter.dart';

abstract class XmtpState extends Equatable {
  const XmtpState();

  @override
  List<Object?> get props => [];
}

/// Initial state
class XmtpInitial extends XmtpState {
  const XmtpInitial();
}

/// Loading state
class XmtpLoading extends XmtpState {
  const XmtpLoading();
}

/// Client created successfully
class XmtpClientCreated extends XmtpState {
  final XmtpClient client;

  const XmtpClientCreated({required this.client});

  @override
  List<Object?> get props => [client];
}

/// Loading conversations
class XmtpLoadingConversations extends XmtpState {
  const XmtpLoadingConversations();
}

/// Conversations loaded
class XmtpConversationsLoaded extends XmtpState {
  final XmtpClient client;
  final List<Conversation> conversations;

  const XmtpConversationsLoaded({
    required this.client,
    required this.conversations,
  });

  @override
  List<Object?> get props => [client, conversations];
}

/// Creating conversation
class XmtpCreatingConversation extends XmtpState {
  const XmtpCreatingConversation();
}

/// Conversation created
class XmtpConversationCreated extends XmtpState {
  final Conversation conversation;

  const XmtpConversationCreated({required this.conversation});

  @override
  List<Object?> get props => [conversation];
}

/// Error state
class XmtpError extends XmtpState {
  final String message;

  const XmtpError({required this.message});

  @override
  List<Object?> get props => [message];
}
